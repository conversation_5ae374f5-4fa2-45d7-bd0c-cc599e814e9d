using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using SRJ.CommonCore.Interfaces;
using System;
using System.Collections.Concurrent;
using System.Text.Json;
using System.Threading.Tasks;

namespace SRJ.CommonCore.Services
{
    /// <summary>
    /// Implementation of caching service using both memory and distributed caching
    /// </summary>
    public class CacheService : ICacheService
    {
        private readonly IMemoryCache _memoryCache;
        private readonly IDistributedCache _distributedCache;
        private readonly ILogger<CacheService> _logger;
        private readonly ConcurrentDictionary<string, bool> _cacheKeys;

        public CacheService(
            IMemoryCache memoryCache,
            IDistributedCache distributedCache,
            ILogger<CacheService> logger)
        {
            _memoryCache = memoryCache;
            _distributedCache = distributedCache;
            _logger = logger;
            _cacheKeys = new ConcurrentDictionary<string, bool>();
        }

        public async Task<T?> GetAsync<T>(string key) where T : class
        {
            try
            {
                // Try memory cache first (faster)
                if (_memoryCache.TryGetValue(key, out T? memoryValue))
                {
                    _logger.LogDebug("Cache hit (memory): {Key}", key);
                    return memoryValue;
                }

                // Try distributed cache
                var distributedValue = await _distributedCache.GetStringAsync(key);
                if (!string.IsNullOrEmpty(distributedValue))
                {
                    var deserializedValue = JsonSerializer.Deserialize<T>(distributedValue);
                    
                    // Store in memory cache for faster access next time
                    _memoryCache.Set(key, deserializedValue, TimeSpan.FromMinutes(5));
                    
                    _logger.LogDebug("Cache hit (distributed): {Key}", key);
                    return deserializedValue;
                }

                _logger.LogDebug("Cache miss: {Key}", key);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cached value for key: {Key}", key);
                return null;
            }
        }

        public async Task SetAsync<T>(string key, T value, TimeSpan expiration) where T : class
        {
            try
            {
                var options = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = expiration,
                    Size = 1
                };

                // Set in memory cache
                _memoryCache.Set(key, value, options);

                // Set in distributed cache
                var serializedValue = JsonSerializer.Serialize(value);
                var distributedOptions = new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = expiration
                };

                await _distributedCache.SetStringAsync(key, serializedValue, distributedOptions);
                
                _cacheKeys.TryAdd(key, true);
                _logger.LogDebug("Cache set: {Key}, Expiration: {Expiration}", key, expiration);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cached value for key: {Key}", key);
            }
        }

        public async Task SetAsync<T>(string key, T value, DateTimeOffset absoluteExpiration) where T : class
        {
            try
            {
                var options = new MemoryCacheEntryOptions
                {
                    AbsoluteExpiration = absoluteExpiration,
                    Size = 1
                };

                // Set in memory cache
                _memoryCache.Set(key, value, options);

                // Set in distributed cache
                var serializedValue = JsonSerializer.Serialize(value);
                var distributedOptions = new DistributedCacheEntryOptions
                {
                    AbsoluteExpiration = absoluteExpiration
                };

                await _distributedCache.SetStringAsync(key, serializedValue, distributedOptions);
                
                _cacheKeys.TryAdd(key, true);
                _logger.LogDebug("Cache set: {Key}, Absolute Expiration: {AbsoluteExpiration}", key, absoluteExpiration);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cached value for key: {Key}", key);
            }
        }

        public async Task RemoveAsync(string key)
        {
            try
            {
                _memoryCache.Remove(key);
                await _distributedCache.RemoveAsync(key);
                _cacheKeys.TryRemove(key, out _);
                
                _logger.LogDebug("Cache removed: {Key}", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cached value for key: {Key}", key);
            }
        }

        public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan expiration) where T : class
        {
            var cachedValue = await GetAsync<T>(key);
            if (cachedValue != null)
            {
                return cachedValue;
            }

            var newValue = await factory();
            if (newValue != null)
            {
                await SetAsync(key, newValue, expiration);
            }

            return newValue;
        }

        public async Task RemoveByPatternAsync(string pattern)
        {
            try
            {
                var keysToRemove = new List<string>();
                
                foreach (var key in _cacheKeys.Keys)
                {
                    if (key.Contains(pattern, StringComparison.OrdinalIgnoreCase))
                    {
                        keysToRemove.Add(key);
                    }
                }

                foreach (var key in keysToRemove)
                {
                    await RemoveAsync(key);
                }

                _logger.LogDebug("Cache removed by pattern: {Pattern}, Count: {Count}", pattern, keysToRemove.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cached values by pattern: {Pattern}", pattern);
            }
        }
    }
}
