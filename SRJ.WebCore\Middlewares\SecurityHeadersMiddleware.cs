using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;

namespace SRJ.WebCore.Middlewares
{
    /// <summary>
    /// Middleware that adds security headers to HTTP responses
    /// </summary>
    public class SecurityHeadersMiddleware
    {
        private readonly RequestDelegate _next;

        public SecurityHeadersMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Add security headers
            AddSecurityHeaders(context.Response);

            await _next(context);
        }

        private static void AddSecurityHeaders(HttpResponse response)
        {
            // Prevent clickjacking
            if (!response.Headers.ContainsKey("X-Frame-Options"))
            {
                response.Headers["X-Frame-Options"] = "DENY";
            }

            // Prevent MIME type sniffing
            if (!response.Headers.ContainsKey("X-Content-Type-Options"))
            {
                response.Headers["X-Content-Type-Options"] = "nosniff";
            }

            // Enable XSS protection
            if (!response.Headers.ContainsKey("X-XSS-Protection"))
            {
                response.Headers["X-XSS-Protection"] = "1; mode=block";
            }

            // Referrer policy
            if (!response.Headers.ContainsKey("Referrer-Policy"))
            {
                response.Headers["Referrer-Policy"] = "strict-origin-when-cross-origin";
            }

            // Content Security Policy
            if (!response.Headers.ContainsKey("Content-Security-Policy"))
            {
                var csp = "default-src 'self'; " +
                         "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " +
                         "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; " +
                         "font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net; " +
                         "img-src 'self' data: https:; " +
                         "connect-src 'self' https:; " +
                         "frame-ancestors 'none'; " +
                         "base-uri 'self'; " +
                         "form-action 'self';";

                response.Headers["Content-Security-Policy"] = csp;
            }

            // Permissions Policy (formerly Feature Policy)
            if (!response.Headers.ContainsKey("Permissions-Policy"))
            {
                response.Headers["Permissions-Policy"] =
                    "camera=(), microphone=(), geolocation=(), payment=(), usb=()";
            }

            // Remove server information
            response.Headers.Remove("Server");
            response.Headers.Remove("X-Powered-By");
            response.Headers.Remove("X-AspNet-Version");
            response.Headers.Remove("X-AspNetMvc-Version");
        }
    }

    /// <summary>
    /// Extension method to add security headers middleware
    /// </summary>
    public static class SecurityHeadersMiddlewareExtensions
    {
        public static IApplicationBuilder UseSecurityHeaders(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<SecurityHeadersMiddleware>();
        }
    }
}
