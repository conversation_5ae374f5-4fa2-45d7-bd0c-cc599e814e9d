# PowerShell script to download DataTables extensions locally
# This script downloads all required DataTables extensions for local use

$baseUrl = "https://cdn.datatables.net"
$extensionsPath = "SRJ.Web/wwwroot/assets/extensions"

# Create directories
$directories = @(
    "$extensionsPath/datatables.net-responsive/js",
    "$extensionsPath/datatables.net-responsive-bs5/js",
    "$extensionsPath/datatables.net-responsive-bs5/css",
    "$extensionsPath/datatables.net-buttons/js",
    "$extensionsPath/datatables.net-buttons-bs5/js",
    "$extensionsPath/datatables.net-buttons-bs5/css",
    "$extensionsPath/jszip",
    "$extensionsPath/pdfmake"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force
        Write-Host "Created directory: $dir"
    }
}

# Download files
$downloads = @(
    # Responsive extension
    @{
        Url = "$baseUrl/responsive/2.5.0/js/dataTables.responsive.min.js"
        Path = "$extensionsPath/datatables.net-responsive/js/dataTables.responsive.min.js"
    },
    @{
        Url = "$baseUrl/responsive/2.5.0/js/responsive.bootstrap5.min.js"
        Path = "$extensionsPath/datatables.net-responsive-bs5/js/responsive.bootstrap5.min.js"
    },
    @{
        Url = "$baseUrl/responsive/2.5.0/css/responsive.bootstrap5.min.css"
        Path = "$extensionsPath/datatables.net-responsive-bs5/css/responsive.bootstrap5.min.css"
    },
    
    # Buttons extension
    @{
        Url = "$baseUrl/buttons/2.3.6/js/dataTables.buttons.min.js"
        Path = "$extensionsPath/datatables.net-buttons/js/dataTables.buttons.min.js"
    },
    @{
        Url = "$baseUrl/buttons/2.3.6/js/buttons.bootstrap5.min.js"
        Path = "$extensionsPath/datatables.net-buttons-bs5/js/buttons.bootstrap5.min.js"
    },
    @{
        Url = "$baseUrl/buttons/2.3.6/css/buttons.bootstrap5.min.css"
        Path = "$extensionsPath/datatables.net-buttons-bs5/css/buttons.bootstrap5.min.css"
    },
    @{
        Url = "$baseUrl/buttons/2.3.6/js/buttons.html5.min.js"
        Path = "$extensionsPath/datatables.net-buttons/js/buttons.html5.min.js"
    },
    @{
        Url = "$baseUrl/buttons/2.3.6/js/buttons.print.min.js"
        Path = "$extensionsPath/datatables.net-buttons/js/buttons.print.min.js"
    },
    @{
        Url = "$baseUrl/buttons/2.3.6/js/buttons.colVis.min.js"
        Path = "$extensionsPath/datatables.net-buttons/js/buttons.colVis.min.js"
    },
    
    # JSZip for Excel export
    @{
        Url = "https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"
        Path = "$extensionsPath/jszip/jszip.min.js"
    },
    
    # pdfmake for PDF export
    @{
        Url = "https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"
        Path = "$extensionsPath/pdfmake/pdfmake.min.js"
    },
    @{
        Url = "https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"
        Path = "$extensionsPath/pdfmake/vfs_fonts.js"
    }
)

# Download each file
foreach ($download in $downloads) {
    try {
        Write-Host "Downloading: $($download.Url)"
        Invoke-WebRequest -Uri $download.Url -OutFile $download.Path -UseBasicParsing
        Write-Host "✓ Downloaded: $($download.Path)"
    }
    catch {
        Write-Host "✗ Failed to download: $($download.Url)"
        Write-Host "Error: $($_.Exception.Message)"
    }
}

Write-Host ""
Write-Host "Download completed! All DataTables extensions are now available locally."
