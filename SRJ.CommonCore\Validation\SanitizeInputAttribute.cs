using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
using System.Web;

namespace SRJ.CommonCore.Validation
{
    /// <summary>
    /// Validation attribute that sanitizes input by removing potentially dangerous characters
    /// </summary>
    public class SanitizeInputAttribute : ValidationAttribute
    {
        private readonly bool _allowHtml;
        private readonly bool _trimWhitespace;

        public SanitizeInputAttribute(bool allowHtml = false, bool trimWhitespace = true)
        {
            _allowHtml = allowHtml;
            _trimWhitespace = trimWhitespace;
        }

        public override bool IsValid(object? value)
        {
            if (value == null || value is not string stringValue)
                return true;

            // Sanitize the input
            var sanitized = SanitizeString(stringValue);
            
            // Update the original value with sanitized version
            if (value is string && sanitized != stringValue)
            {
                // Note: This approach has limitations in MVC model binding
                // Consider using a custom model binder for more robust sanitization
                value = sanitized;
            }

            return true;
        }

        private string SanitizeString(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            var result = input;

            // Trim whitespace if requested
            if (_trimWhitespace)
            {
                result = result.Trim();
            }

            // Remove or encode HTML if not allowed
            if (!_allowHtml)
            {
                result = HttpUtility.HtmlEncode(result);
            }

            // Remove potentially dangerous characters
            result = RemoveDangerousCharacters(result);

            // Remove SQL injection patterns
            result = RemoveSqlInjectionPatterns(result);

            // Remove script injection patterns
            result = RemoveScriptInjectionPatterns(result);

            return result;
        }

        private static string RemoveDangerousCharacters(string input)
        {
            // Remove null bytes and other control characters
            return Regex.Replace(input, @"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]", "", RegexOptions.Compiled);
        }

        private static string RemoveSqlInjectionPatterns(string input)
        {
            var sqlPatterns = new[]
            {
                @"(\b(ALTER|CREATE|DELETE|DROP|EXEC(UTE)?|INSERT( +INTO)?|MERGE|SELECT|UPDATE|UNION( +ALL)?)\b)",
                @"(\b(AND|OR)\b.*(=|>|<|!=|<>|<=|>=))",
                @"(\b(CAST|CONVERT|CHAR|NCHAR|VARCHAR|NVARCHAR)\s*\()",
                @"(\b(WAITFOR|DELAY)\s+)",
                @"(\b(OPENROWSET|OPENDATASOURCE)\s*\()",
                @"(\b(sp_|xp_)\w+)",
                @"(--|/\*|\*/|;|\||&)"
            };

            foreach (var pattern in sqlPatterns)
            {
                input = Regex.Replace(input, pattern, "", RegexOptions.IgnoreCase | RegexOptions.Compiled);
            }

            return input;
        }

        private static string RemoveScriptInjectionPatterns(string input)
        {
            var scriptPatterns = new[]
            {
                @"<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>",
                @"javascript:",
                @"vbscript:",
                @"onload\s*=",
                @"onerror\s*=",
                @"onclick\s*=",
                @"onmouseover\s*=",
                @"onfocus\s*=",
                @"onblur\s*=",
                @"onchange\s*=",
                @"onsubmit\s*=",
                @"<iframe\b[^>]*>",
                @"<object\b[^>]*>",
                @"<embed\b[^>]*>",
                @"<link\b[^>]*>",
                @"<meta\b[^>]*>"
            };

            foreach (var pattern in scriptPatterns)
            {
                input = Regex.Replace(input, pattern, "", RegexOptions.IgnoreCase | RegexOptions.Compiled);
            }

            return input;
        }
    }
}
