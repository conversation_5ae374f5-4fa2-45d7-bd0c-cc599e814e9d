using System;
using System.Threading.Tasks;

namespace SRJ.CommonCore.Interfaces
{
    /// <summary>
    /// Interface for caching service that provides both memory and distributed caching capabilities
    /// </summary>
    public interface ICacheService
    {
        /// <summary>
        /// Gets a cached value by key
        /// </summary>
        /// <typeparam name="T">Type of the cached value</typeparam>
        /// <param name="key">Cache key</param>
        /// <returns>Cached value or default if not found</returns>
        Task<T?> GetAsync<T>(string key) where T : class;

        /// <summary>
        /// Sets a value in cache with expiration
        /// </summary>
        /// <typeparam name="T">Type of the value to cache</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="value">Value to cache</param>
        /// <param name="expiration">Cache expiration time</param>
        /// <returns>Task</returns>
        Task SetAsync<T>(string key, T value, TimeSpan expiration) where T : class;

        /// <summary>
        /// Sets a value in cache with absolute expiration
        /// </summary>
        /// <typeparam name="T">Type of the value to cache</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="value">Value to cache</param>
        /// <param name="absoluteExpiration">Absolute expiration time</param>
        /// <returns>Task</returns>
        Task SetAsync<T>(string key, T value, DateTimeOffset absoluteExpiration) where T : class;

        /// <summary>
        /// Removes a cached value by key
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Task</returns>
        Task RemoveAsync(string key);

        /// <summary>
        /// Gets or sets a cached value using a factory function
        /// </summary>
        /// <typeparam name="T">Type of the cached value</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="factory">Factory function to create the value if not cached</param>
        /// <param name="expiration">Cache expiration time</param>
        /// <returns>Cached or newly created value</returns>
        Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan expiration) where T : class;

        /// <summary>
        /// Removes all cached values with keys matching the pattern
        /// </summary>
        /// <param name="pattern">Pattern to match cache keys</param>
        /// <returns>Task</returns>
        Task RemoveByPatternAsync(string pattern);
    }
}
