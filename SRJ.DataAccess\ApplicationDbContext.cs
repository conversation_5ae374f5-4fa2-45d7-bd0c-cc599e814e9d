﻿using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using SRJ.DataAccess.Entities;
using SRJ.DataAccess.Entities.Application;
using SRJ.DataAccess.Identity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SRJ.DataAccess
{
    public partial class ApplicationDbContext : IdentityDbContext<AppUser, AppRole, long>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        //Payment Myfatoorah      
        public DbSet<PaymentLogTable> PaymentLogTables { get; set; }
        public DbSet<PaymentStatusTable> PaymentStatusTables { get; set; }

        //File Table
        public DbSet<FileModel> Files { get; set; }

        //Custom Table
        public DbSet<ApplicationForm> Applications { get; set; }

        //Identity Table
        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);
            builder.HasDefaultSchema("Identity");

            builder.Entity<AppUser>(entity =>
            {
                entity.ToTable(name: "User");
            });

            builder.Entity<AppRole>(entity =>
            {
                entity.ToTable(name: "Role");
            });

            builder.Entity<IdentityUserRole<long>>(entity =>
            {
                entity.ToTable("UserRoles");
                entity.HasKey(key => new { key.UserId, key.RoleId });
            });

            builder.Entity<IdentityUserClaim<long>>(entity =>
            {
                entity.ToTable("UserClaims");
                entity.HasKey(key => key.Id);
            });

            builder.Entity<IdentityUserLogin<long>>(entity =>
            {
                entity.ToTable("UserLogins");
                entity.HasKey(key => new { key.ProviderKey, key.LoginProvider });
            });

            builder.Entity<IdentityRoleClaim<long>>(entity =>
            {
                entity.ToTable("RoleClaims");
                entity.HasKey(key => key.Id);
            });

            builder.Entity<IdentityUserToken<long>>(entity =>
            {
                entity.ToTable("UserTokens");
                entity.HasKey(key => new { key.UserId, key.LoginProvider, key.Name });
            });

            // Configure indexes for better performance
            ConfigureIndexes(builder);
        }

        private void ConfigureIndexes(ModelBuilder builder)
        {
            // ApplicationForm indexes
            builder.Entity<ApplicationForm>(entity =>
            {
                entity.HasIndex(e => e.CivilId).HasDatabaseName("IX_Applications_CivilId");
                entity.HasIndex(e => e.Email).HasDatabaseName("IX_Applications_Email");
                entity.HasIndex(e => e.MobileNumber).HasDatabaseName("IX_Applications_MobileNumber");
                entity.HasIndex(e => e.InvoiceId).HasDatabaseName("IX_Applications_InvoiceId");
                entity.HasIndex(e => e.ApplicationStatus).HasDatabaseName("IX_Applications_ApplicationStatus");
                entity.HasIndex(e => e.ApplicationStage).HasDatabaseName("IX_Applications_ApplicationStage");
                entity.HasIndex(e => e.CreatedDate).HasDatabaseName("IX_Applications_CreatedDate");
                entity.HasIndex(e => e.UniqueId).HasDatabaseName("IX_Applications_UniqueId");
                entity.HasIndex(e => new { e.CivilId, e.ApplicationStatus }).HasDatabaseName("IX_Applications_CivilId_Status");
            });

            // PaymentStatusTable indexes
            builder.Entity<PaymentStatusTable>(entity =>
            {
                entity.HasIndex(e => e.CivilId).HasDatabaseName("IX_PaymentStatus_CivilId");
                entity.HasIndex(e => e.InvoiceId).HasDatabaseName("IX_PaymentStatus_InvoiceId");
                entity.HasIndex(e => e.PaymentId).HasDatabaseName("IX_PaymentStatus_PaymentId");
                entity.HasIndex(e => e.InvoiceStatus).HasDatabaseName("IX_PaymentStatus_InvoiceStatus");
                entity.HasIndex(e => e.CustomerReference).HasDatabaseName("IX_PaymentStatus_CustomerReference");
                entity.HasIndex(e => e.CreatedDate).HasDatabaseName("IX_PaymentStatus_CreatedDate");
                entity.HasIndex(e => new { e.CivilId, e.InvoiceStatus }).HasDatabaseName("IX_PaymentStatus_CivilId_Status");
            });

            // PaymentLogTable indexes
            builder.Entity<PaymentLogTable>(entity =>
            {
                entity.HasIndex(e => e.InvoiceId).HasDatabaseName("IX_PaymentLog_InvoiceId");
                entity.HasIndex(e => e.CustomerReference).HasDatabaseName("IX_PaymentLog_CustomerReference");
                entity.HasIndex(e => e.CreatedDate).HasDatabaseName("IX_PaymentLog_CreatedDate");
            });

            // FileModel indexes
            builder.Entity<FileModel>(entity =>
            {
                entity.HasIndex(e => e.OwnerEntityId).HasDatabaseName("IX_Files_OwnerEntityId");
                entity.HasIndex(e => e.OwnerEntityType).HasDatabaseName("IX_Files_OwnerEntityType");
                entity.HasIndex(e => new { e.OwnerEntityId, e.OwnerEntityType }).HasDatabaseName("IX_Files_Owner");
                entity.HasIndex(e => e.CreatedDate).HasDatabaseName("IX_Files_CreatedDate");
            });

            // AppUser indexes (additional to Identity defaults)
            builder.Entity<AppUser>(entity =>
            {
                entity.HasIndex(e => e.Status).HasDatabaseName("IX_User_Status");
                entity.HasIndex(e => e.CreatedAt).HasDatabaseName("IX_User_CreatedAt");
                entity.HasIndex(e => e.AzureAdObjectId).HasDatabaseName("IX_User_AzureAdObjectId");
            });

            // BaseEntity common indexes for all entities that inherit from it
            builder.Entity<ApplicationForm>().HasIndex(e => e.IsDeleted).HasDatabaseName("IX_Applications_IsDeleted");
            builder.Entity<PaymentStatusTable>().HasIndex(e => e.IsDeleted).HasDatabaseName("IX_PaymentStatus_IsDeleted");
            builder.Entity<PaymentLogTable>().HasIndex(e => e.IsDeleted).HasDatabaseName("IX_PaymentLog_IsDeleted");
            builder.Entity<FileModel>().HasIndex(e => e.IsDeleted).HasDatabaseName("IX_Files_IsDeleted");
        }
    }
}
