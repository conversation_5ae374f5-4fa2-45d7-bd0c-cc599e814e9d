﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace SRJ.Web.Controllers
{
    [Route("Error")]
    public class ErrorController : Controller
    {
        [HttpGet("401")]
        [AllowAnonymous]
        public IActionResult Error401()
        {
            return View();
        }

        [HttpGet("403")]
        [AllowAnonymous]
        public IActionResult Error403()
        {
            return View();
        }

        [HttpGet("404")]
        [AllowAnonymous]
        public IActionResult Error404()
        {
            return View();
        }

        [HttpGet("500")]
        [AllowAnonymous]
        public IActionResult Error500()
        {
            return View();
        }
    }
}
