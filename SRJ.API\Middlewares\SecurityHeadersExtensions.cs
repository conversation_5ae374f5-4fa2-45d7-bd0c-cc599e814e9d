namespace SRJ.API.Middlewares
{
    /// <summary>
    /// Extension methods for adding security headers to API responses
    /// </summary>
    public static class SecurityHeadersExtensions
    {
        public static IApplicationBuilder UseSecurityHeaders(this IApplicationBuilder app)
        {
            return app.Use(async (context, next) =>
            {
                // Add security headers
                var response = context.Response;

                // Prevent clickjacking
                if (!response.Headers.ContainsKey("X-Frame-Options"))
                {
                    response.Headers.Add("X-Frame-Options", "DENY");
                }

                // Prevent MIME type sniffing
                if (!response.Headers.ContainsKey("X-Content-Type-Options"))
                {
                    response.Headers.Add("X-Content-Type-Options", "nosniff");
                }

                // Enable XSS protection
                if (!response.Headers.ContainsKey("X-XSS-Protection"))
                {
                    response.Headers.Add("X-XSS-Protection", "1; mode=block");
                }

                // Referrer policy
                if (!response.Headers.ContainsKey("Referrer-Policy"))
                {
                    response.Headers.Add("Referrer-Policy", "strict-origin-when-cross-origin");
                }

                // Content Security Policy for API
                if (!response.Headers.ContainsKey("Content-Security-Policy"))
                {
                    response.Headers.Add("Content-Security-Policy", "default-src 'none'; frame-ancestors 'none';");
                }

                // Remove server information
                response.Headers.Remove("Server");
                response.Headers.Remove("X-Powered-By");
                response.Headers.Remove("X-AspNet-Version");
                response.Headers.Remove("X-AspNetMvc-Version");

                await next();
            });
        }
    }
}
