﻿using System.Reflection;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using SRJ.DataAccess;
using SRJ.WebCore.ACL.VM;

namespace SRJ.WebCore.ACL
{
    /// <summary>
    /// Provides dynamic authorization functionality by verifying user permissions for specific actions.
    /// </summary>
    public class DynamicAuthorizationFilter : IAsyncAuthorizationFilter
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IConfiguration _configuration;

        /// <summary>
        /// Initializes a new instance of the <see cref="DynamicAuthorizationFilter"/> class.
        /// </summary>
        /// <param name="dbContext">The application's database context.</param>
        /// <param name="configuration">The application configuration.</param>
        public DynamicAuthorizationFilter(ApplicationDbContext dbContext, IConfiguration configuration)
        {
            _dbContext = dbContext;
            _configuration = configuration;
        }

        /// <summary>
        /// Asynchronously determines whether the current user is authorized to access the requested resource.
        /// </summary>
        /// <param name="context">The authorization filter context.</param>
        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            // Skip loading roles if the user is not authenticated or roles are already loaded
            if (!context.HttpContext.User.Identity.IsAuthenticated || context.HttpContext.Items.ContainsKey("UserRoles"))
                return;

            var userName = context.HttpContext.User.Identity.Name;

            if (!string.IsNullOrEmpty(userName))
            {
                var userRoles = await (
                    from user in _dbContext.Users
                    join userRole in _dbContext.UserRoles on user.Id equals userRole.UserId
                    join role in _dbContext.Roles on userRole.RoleId equals role.Id
                    where user.UserName == userName
                    select role.Name
                ).ToListAsync();

                // Store roles in HttpContext.Items for later use in the request
                context.HttpContext.Items["UserRoles"] = userRoles;
            }

            if (HasAllowAnonymous(context))
            {
                return;
            }

            if (!IsUserAuthenticated(context))
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            var actionId = GetActionId(context);

            // Check if user is admin based on configuration
            var adminEmails = _configuration.GetSection("AdminEmails").Get<string[]>() ?? Array.Empty<string>();
            if (!string.IsNullOrEmpty(userName) && adminEmails.Contains(userName, StringComparer.OrdinalIgnoreCase))
            {
                return;
            }

            var roles = await (
                from user in _dbContext.Users
                join userRole in _dbContext.UserRoles on user.Id equals userRole.UserId
                join role in _dbContext.Roles on userRole.RoleId equals role.Id
                where user.UserName == userName
                select role
            ).ToListAsync();

            foreach (var role in roles)
            {

                if (string.IsNullOrWhiteSpace(role.Access))
                    continue;

                var accessList = JsonConvert.DeserializeObject<IEnumerable<MvcControllerInfo>>(role.Access);

                if (accessList.SelectMany(c => c.Actions).Any(a => a.Id == actionId))
                    return;
            }

            context.Result = new ForbidResult();
        }

        /// <summary>
        /// Checks if the action or its controller has an Authorize attribute, requiring authentication.
        /// </summary>
        /// <param name="context">The authorization filter context.</param>
        /// <returns>True if the action or controller is protected; otherwise, false.</returns>
        private bool IsProtectedAction(AuthorizationFilterContext context)
        {
            if (context.Filters.Any(item => item is IAllowAnonymousFilter))
                return false;

            var controllerActionDescriptor = (ControllerActionDescriptor)context.ActionDescriptor;
            var controllerTypeInfo = controllerActionDescriptor.ControllerTypeInfo;
            var actionMethodInfo = controllerActionDescriptor.MethodInfo;

            var authorizeAttribute = controllerTypeInfo.GetCustomAttribute<AuthorizeAttribute>();
            if (authorizeAttribute != null)
                return true;

            authorizeAttribute = actionMethodInfo.GetCustomAttribute<AuthorizeAttribute>();
            if (authorizeAttribute != null)
                return true;

            return false;
        }

        /// <summary>
        /// Determines if the current user is authenticated.
        /// </summary>
        /// <param name="context">The authorization filter context.</param>
        /// <returns>True if the user is authenticated; otherwise, false.</returns>
        private bool IsUserAuthenticated(AuthorizationFilterContext context)
        {
            return context.HttpContext.User.Identity.IsAuthenticated;
        }

        /// <summary>
        /// Checks if the action has an AllowAnonymous attribute to bypass authorization.
        /// </summary>
        /// <param name="context">The authorization filter context.</param>
        /// <returns>True if the action allows anonymous access; otherwise, false.</returns>
        private bool HasAllowAnonymous(AuthorizationFilterContext context)
        {
            var filters = context.Filters;
            for (var i = 0; i < filters.Count; i++)
            {
                if (filters[i] is IAllowAnonymousFilter)
                {
                    return true;
                }
            }

            // When doing endpoint routing, MVC does not add AllowAnonymousFilters for 
            // AllowAnonymousAttributes that were discovered on controllers and actions. To maintain 
            // compatibility with 2.x, we'll check for the presence of IAllowAnonymous in endpoint metadata.
            var endpoint = context.HttpContext.GetEndpoint();
            if (endpoint?.Metadata?.GetMetadata<IAllowAnonymous>() != null)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Generates a unique identifier for the action being accessed, based on its area, controller, and action names.
        /// </summary>
        /// <param name="context">The authorization filter context.</param>
        /// <returns>A string representing the unique action identifier.</returns>
        private string GetActionId(AuthorizationFilterContext context)
        {
            var controllerActionDescriptor = (ControllerActionDescriptor)context.ActionDescriptor;
            var area = controllerActionDescriptor.ControllerTypeInfo.GetCustomAttribute<AreaAttribute>()?.RouteValue;
            var controller = controllerActionDescriptor.ControllerName;
            var action = controllerActionDescriptor.ActionName;

            return $"{area}:{controller}:{action}";
        }
    }
}
