-- Performance Indexes Script
-- This script adds database indexes to improve query performance

SET ANSI_NULLS ON
SET QUOTED_IDENTIFIER ON

-- First, alter column types to make them indexable (nvarchar(450) max for index keys)
-- ApplicationForm columns
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[Identity].[Applications]') AND name = 'CivilId' AND max_length = -1)
    ALTER TABLE [Identity].[Applications] ALTER COLUMN CivilId nvarchar(450) NULL;

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[Identity].[Applications]') AND name = 'Email' AND max_length = -1)
    ALTER TABLE [Identity].[Applications] ALTER COLUMN Email nvarchar(450) NULL;

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[Identity].[Applications]') AND name = 'MobileNumber' AND max_length = -1)
    ALTER TABLE [Identity].[Applications] ALTER COLUMN MobileNumber nvarchar(450) NULL;

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[Identity].[Applications]') AND name = 'InvoiceId' AND max_length = -1)
    ALTER TABLE [Identity].[Applications] ALTER COLUMN InvoiceId nvarchar(450) NULL;

-- PaymentStatusTable columns
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[Identity].[PaymentStatusTables]') AND name = 'CivilId' AND max_length = -1)
    ALTER TABLE [Identity].[PaymentStatusTables] ALTER COLUMN CivilId nvarchar(450) NULL;

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[Identity].[PaymentStatusTables]') AND name = 'InvoiceId' AND max_length = -1)
    ALTER TABLE [Identity].[PaymentStatusTables] ALTER COLUMN InvoiceId nvarchar(450) NULL;

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[Identity].[PaymentStatusTables]') AND name = 'PaymentId' AND max_length = -1)
    ALTER TABLE [Identity].[PaymentStatusTables] ALTER COLUMN PaymentId nvarchar(450) NULL;

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[Identity].[PaymentStatusTables]') AND name = 'InvoiceStatus' AND max_length = -1)
    ALTER TABLE [Identity].[PaymentStatusTables] ALTER COLUMN InvoiceStatus nvarchar(450) NULL;

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[Identity].[PaymentStatusTables]') AND name = 'CustomerReference' AND max_length = -1)
    ALTER TABLE [Identity].[PaymentStatusTables] ALTER COLUMN CustomerReference nvarchar(450) NULL;

-- PaymentLogTable columns
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[Identity].[PaymentLogTables]') AND name = 'InvoiceId' AND max_length = -1)
    ALTER TABLE [Identity].[PaymentLogTables] ALTER COLUMN InvoiceId nvarchar(450) NULL;

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[Identity].[PaymentLogTables]') AND name = 'CustomerReference' AND max_length = -1)
    ALTER TABLE [Identity].[PaymentLogTables] ALTER COLUMN CustomerReference nvarchar(450) NULL;

-- Files columns
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[Identity].[Files]') AND name = 'OwnerEntityType' AND max_length = -1)
    ALTER TABLE [Identity].[Files] ALTER COLUMN OwnerEntityType nvarchar(450) NULL;

PRINT 'Column types updated for indexing.'

-- ApplicationForm indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Applications_CivilId' AND object_id = OBJECT_ID('[Identity].[Applications]'))
    CREATE INDEX IX_Applications_CivilId ON [Identity].[Applications] (CivilId);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Applications_Email' AND object_id = OBJECT_ID('[Identity].[Applications]'))
    CREATE INDEX IX_Applications_Email ON [Identity].[Applications] (Email);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Applications_MobileNumber' AND object_id = OBJECT_ID('[Identity].[Applications]'))
    CREATE INDEX IX_Applications_MobileNumber ON [Identity].[Applications] (MobileNumber);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Applications_InvoiceId' AND object_id = OBJECT_ID('[Identity].[Applications]'))
    CREATE INDEX IX_Applications_InvoiceId ON [Identity].[Applications] (InvoiceId);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Applications_ApplicationStatus' AND object_id = OBJECT_ID('[Identity].[Applications]'))
    CREATE INDEX IX_Applications_ApplicationStatus ON [Identity].[Applications] (ApplicationStatus);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Applications_ApplicationStage' AND object_id = OBJECT_ID('[Identity].[Applications]'))
    CREATE INDEX IX_Applications_ApplicationStage ON [Identity].[Applications] (ApplicationStage);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Applications_CreatedDate' AND object_id = OBJECT_ID('[Identity].[Applications]'))
    CREATE INDEX IX_Applications_CreatedDate ON [Identity].[Applications] (CreatedDate);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Applications_UniqueId' AND object_id = OBJECT_ID('[Identity].[Applications]'))
    CREATE INDEX IX_Applications_UniqueId ON [Identity].[Applications] (UniqueId);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Applications_IsDeleted' AND object_id = OBJECT_ID('[Identity].[Applications]'))
    CREATE INDEX IX_Applications_IsDeleted ON [Identity].[Applications] (IsDeleted);

-- Composite index removed due to key length limitations
-- IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Applications_CivilId_Status' AND object_id = OBJECT_ID('[Identity].[Applications]'))
--     CREATE INDEX IX_Applications_CivilId_Status ON [Identity].[Applications] (CivilId, ApplicationStatus);

-- PaymentStatusTable indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PaymentStatus_CivilId' AND object_id = OBJECT_ID('[Identity].[PaymentStatusTables]'))
    CREATE INDEX IX_PaymentStatus_CivilId ON [Identity].[PaymentStatusTables] (CivilId);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PaymentStatus_InvoiceId' AND object_id = OBJECT_ID('[Identity].[PaymentStatusTables]'))
    CREATE INDEX IX_PaymentStatus_InvoiceId ON [Identity].[PaymentStatusTables] (InvoiceId);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PaymentStatus_PaymentId' AND object_id = OBJECT_ID('[Identity].[PaymentStatusTables]'))
    CREATE INDEX IX_PaymentStatus_PaymentId ON [Identity].[PaymentStatusTables] (PaymentId);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PaymentStatus_InvoiceStatus' AND object_id = OBJECT_ID('[Identity].[PaymentStatusTables]'))
    CREATE INDEX IX_PaymentStatus_InvoiceStatus ON [Identity].[PaymentStatusTables] (InvoiceStatus);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PaymentStatus_CustomerReference' AND object_id = OBJECT_ID('[Identity].[PaymentStatusTables]'))
    CREATE INDEX IX_PaymentStatus_CustomerReference ON [Identity].[PaymentStatusTables] (CustomerReference);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PaymentStatus_CreatedDate' AND object_id = OBJECT_ID('[Identity].[PaymentStatusTables]'))
    CREATE INDEX IX_PaymentStatus_CreatedDate ON [Identity].[PaymentStatusTables] (CreatedDate);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PaymentStatus_IsDeleted' AND object_id = OBJECT_ID('[Identity].[PaymentStatusTables]'))
    CREATE INDEX IX_PaymentStatus_IsDeleted ON [Identity].[PaymentStatusTables] (IsDeleted);

-- Composite index removed due to key length limitations
-- IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PaymentStatus_CivilId_Status' AND object_id = OBJECT_ID('[Identity].[PaymentStatusTables]'))
--     CREATE INDEX IX_PaymentStatus_CivilId_Status ON [Identity].[PaymentStatusTables] (CivilId, InvoiceStatus);

-- PaymentLogTable indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PaymentLog_InvoiceId' AND object_id = OBJECT_ID('[Identity].[PaymentLogTables]'))
    CREATE INDEX IX_PaymentLog_InvoiceId ON [Identity].[PaymentLogTables] (InvoiceId);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PaymentLog_CustomerReference' AND object_id = OBJECT_ID('[Identity].[PaymentLogTables]'))
    CREATE INDEX IX_PaymentLog_CustomerReference ON [Identity].[PaymentLogTables] (CustomerReference);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PaymentLog_CreatedDate' AND object_id = OBJECT_ID('[Identity].[PaymentLogTables]'))
    CREATE INDEX IX_PaymentLog_CreatedDate ON [Identity].[PaymentLogTables] (CreatedDate);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PaymentLog_IsDeleted' AND object_id = OBJECT_ID('[Identity].[PaymentLogTables]'))
    CREATE INDEX IX_PaymentLog_IsDeleted ON [Identity].[PaymentLogTables] (IsDeleted);

-- FileModel indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Files_OwnerEntityId' AND object_id = OBJECT_ID('[Identity].[Files]'))
    CREATE INDEX IX_Files_OwnerEntityId ON [Identity].[Files] (OwnerEntityId);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Files_OwnerEntityType' AND object_id = OBJECT_ID('[Identity].[Files]'))
    CREATE INDEX IX_Files_OwnerEntityType ON [Identity].[Files] (OwnerEntityType);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Files_CreatedDate' AND object_id = OBJECT_ID('[Identity].[Files]'))
    CREATE INDEX IX_Files_CreatedDate ON [Identity].[Files] (CreatedDate);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Files_IsDeleted' AND object_id = OBJECT_ID('[Identity].[Files]'))
    CREATE INDEX IX_Files_IsDeleted ON [Identity].[Files] (IsDeleted);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Files_Owner' AND object_id = OBJECT_ID('[Identity].[Files]'))
    CREATE INDEX IX_Files_Owner ON [Identity].[Files] (OwnerEntityId, OwnerEntityType);

-- AppUser indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_User_Status' AND object_id = OBJECT_ID('[Identity].[User]'))
    CREATE INDEX IX_User_Status ON [Identity].[User] (Status);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_User_CreatedAt' AND object_id = OBJECT_ID('[Identity].[User]'))
    CREATE INDEX IX_User_CreatedAt ON [Identity].[User] (CreatedAt);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_User_AzureAdObjectId' AND object_id = OBJECT_ID('[Identity].[User]'))
    CREATE INDEX IX_User_AzureAdObjectId ON [Identity].[User] (AzureAdObjectId);

PRINT 'Performance indexes have been created successfully.';
