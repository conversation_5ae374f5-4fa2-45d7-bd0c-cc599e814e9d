using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace SRJ.CommonCore.Validation
{
    /// <summary>
    /// Validation attribute that prevents script injection
    /// </summary>
    public class NoScriptAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value == null || value is not string stringValue)
                return true;

            // Check for script injection patterns
            var scriptPatterns = new[]
            {
                @"<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>",
                @"javascript:",
                @"vbscript:",
                @"data:text/html",
                @"onload\s*=",
                @"onerror\s*=",
                @"onclick\s*=",
                @"onmouseover\s*=",
                @"onfocus\s*=",
                @"onblur\s*=",
                @"onchange\s*=",
                @"onsubmit\s*=",
                @"<iframe\b[^>]*>",
                @"<object\b[^>]*>",
                @"<embed\b[^>]*>",
                @"<form\b[^>]*>",
                @"<input\b[^>]*>",
                @"<link\b[^>]*>",
                @"<meta\b[^>]*>"
            };

            foreach (var pattern in scriptPatterns)
            {
                if (Regex.IsMatch(stringValue, pattern, RegexOptions.IgnoreCase))
                {
                    ErrorMessage = "Input contains potentially dangerous script content.";
                    return false;
                }
            }

            return true;
        }
    }
}
