@using SRJ.DataAccess.Enums;
@using SRJ.DataAccess.Enums.Application;
@using SRJ.CommonCore.Helpers;

@{
    ViewData["Title"] = "Import Applications";
    var userGenderEnumJson = EnumHelper<UserGender>.GetEnumDisplayValues<UserGender>();
    var fundingTypeEnumJson = EnumHelper<FundingType>.GetEnumDisplayValues<FundingType>();
    var graduateTypeEnumJson = EnumHelper<GraduateType>.GetEnumDisplayValues<GraduateType>();
}

<div class="page-heading">
    <div class="page-title">
        <div class="row">
            <div class="col-12 col-md-6 order-md-1 order-last">
                <h3>Import Applications</h3>
            </div>
            <div class="col-12 col-md-6 order-md-2 order-first">
                <nav aria-label="breadcrumb" class="breadcrumb-header float-start float-lg-end">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="Home">Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Import Applications</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>
<div class="page-content">
    <section class="section">
        <div id="ImportPage">
            <div class="ImportPage-form-outer">
                <div class="card">
                    <div class="card-header">
                        <a asp-action="Index" class="btn btn-primary float-sm-right">
                            <i class="bi bi-arrow-left-circle me-1"></i>
                            Back to List
                        </a>
                    </div>
                    <div class="card-body">
                        <form id="filter-form" class="mb-3">
                            <div class="row">
                                <div class="col-md-4 d-none">
                                    <div class="form-group">
                                        <label for="perPage">Per Page</label>
                                        <input type="number" id="perPage" name="perPage" class="form-control" min="15" max="150" value="@(ViewBag.PerPage ?? 15)" />
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="agent">Agent</label>
                                        <select id="agent" name="agent" class="form-select">
                                            <option value="">Select Agent</option>
                                            @foreach (Agent type in Enum.GetValues(typeof(Agent)))
                                            {
                                                <!option value="@(EnumHelper<Agent>.GetDisplayValue(type))" @(ViewBag.Agent == EnumHelper<Agent>.GetDisplayValue(type) ? "selected" : "")>
                                                    @(EnumHelper<Agent>.GetDisplayValue(type))
                                                </!option>
                                            }
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <button type="button" id="apply-filters" class="btn btn-primary">Apply Filters</button>
                        </form>
                        <form asp-action="SaveImportedData" method="post">
                            <table id="datatable" class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Full Name</th>
                                        <th>Civil ID</th>
                                        <th>Gender</th>
                                        <th>Number</th>
                                        <th>Funding Type</th>
                                        <th>Graduate Type</th>
                                        <th>Agent</th>
                                        <th>CRM Link</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                            </table>
                            <button type="submit" class="btn btn-success">Save All Imported Data</button>
                            <a asp-action="Index" class="btn btn-danger">Cancel</a>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

@section Scripts {
    <script>
        const UserGenderEnum = @Html.Raw(userGenderEnumJson);
        const FundingTypeEnum = @Html.Raw(fundingTypeEnumJson);
        const GraduateTypeEnum = @Html.Raw(graduateTypeEnumJson);

        $(document).ready(function () {
            var table = $("#datatable").DataTable({
                "processing": true,
                "serverSide": true,
                "filter": true,
                "autoWidth": true,
                "lengthMenu": [10, 25, 50, 150, 250, 1000],
                "responsive": true,
                "ajax": {
                    "url": '@Url.Action("ImportData", "Application")',
                    "type": "GET",
                    "data": function (d) {
                        d.agent = $('#agent').val();
                        d.perPage = $('#perPage').val() || 15;
                    }
                },
                "columns": [
                    { "data": "dealId", "render": function (data, type, row) { return data || 'N/A'; } },
                    { "data": "name", "render": function (data, type, row) { return data || 'N/A'; } },
                    { "data": "civilId", "render": function (data, type, row) { return data || 'N/A'; } },
                    { "data": "gender", "render": function (data, type, row) { return UserGenderEnum[data] || 'N/A'; } },
                    { "data": "mobileNumber", "render": function (data, type, row) { return data || 'N/A'; } },
                    { "data": "fundingType", "render": function (data, type, row) { return FundingTypeEnum[data] || 'N/A'; } },
                    { "data": "graduateType", "render": function (data, type, row) { return GraduateTypeEnum[data] || 'N/A'; } },
                    { "data": "agent", "render": function (data, type, row) { return data || 'N/A'; } },
                    {
                        "data": "dealId", "render": function (data, type, row) {
                            return data ? `<a target="_blank" href="https://crm.ktech.edu.kw/deals/${data}">CRM<i class="bi bi-box-arrow-up-right"></i></a>` : 'N/A';
                        }
                    },
                    {
                        "data": null,
                        "render": function (data, type, row) {
                            return `<button type="button" class="btn btn-success save-btn" data-id="${data.dealId || 'N/A'}">Save</button>`;
                        }
                    }
                ],
                "dom": 'lBfrtip',
                "buttons": ["csv", "excel", "pdf", "colvis"],
                "rowCallback": function (row, data, index) {
                    // Append hidden fields for each data field
                    $(row).append(`
                                        <input type="hidden" name="applications[${index}].Name" value="${data.name || ''}" />
                                        <input type="hidden" name="applications[${index}].CivilName" value="${data.civilName || ''}" />
                                        <input type="hidden" name="applications[${index}].CivilId" value="${data.civilId || ''}" />
                                        <input type="hidden" name="applications[${index}].Gender" value="${data.gender || ''}" />
                                        <input type="hidden" name="applications[${index}].MobileNumber" value="${data.mobileNumber || ''}" />
                                        <input type="hidden" name="applications[${index}].FundingType" value="${data.fundingType || ''}" />
                                        <input type="hidden" name="applications[${index}].GraduateType" value="${data.graduateType || ''}" />
                                        <input type="hidden" name="applications[${index}].Agent" value="${data.agent || ''}" />
                                        <input type="hidden" name="applications[${index}].Nationality" value="${data.nationality || ''}" />
                                        <input type="hidden" name="applications[${index}].EmergencyContact1" value="${data.emergencyContact || ''}" />
                                        <input type="hidden" name="applications[${index}].ContactNumber" value="${data.contactNumber || ''}" />
                                        <input type="hidden" name="applications[${index}].ApplicationStatus" value="${data.applicationStatus || ''}" />
                                        <input type="hidden" name="applications[${index}].Source" value="${data.source || ''}" />
                                    `);
                }
            });

            $('#apply-filters').on('click', function () {
                table.ajax.reload();
            });

            $('#datatable').on('click', '.save-btn', function () {
                var row = table.row($(this).parents('tr')).data();
                var data = {
                    Name: row.name,
                    CivilId: row.civilId,
                    Gender: row.gender,
                    MobileNumber: row.mobileNumber,
                    FundingType: row.fundingType,
                    GraduateType: row.graduateType,
                    Agent: row.agent,
                    Nationality: row.nationality,
                    EmergencyContact1: row.emergencyContact,
                    ContactNumber: row.contactNumber,
                    ApplicationStatus: row.applicationStatus,
                    Source: row.source
                };

                $.ajax({
                    url: '@Url.Action("SaveSingleData", "Application")',
                    type: 'POST',
                    data: data,
                });
            });
        });
    </script>
}