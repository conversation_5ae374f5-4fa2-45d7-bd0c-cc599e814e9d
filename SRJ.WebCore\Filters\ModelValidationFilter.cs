using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace SRJ.WebCore.Filters
{
    /// <summary>
    /// Action filter that validates model state and returns appropriate responses
    /// </summary>
    public class ModelValidationFilter : ActionFilterAttribute
    {
        public override void OnActionExecuting(ActionExecutingContext context)
        {
            if (!context.ModelState.IsValid)
            {
                // For API controllers, return JSON response
                if (context.Controller is ControllerBase)
                {
                    var errors = context.ModelState
                        .Where(x => x.Value?.Errors.Count > 0)
                        .ToDictionary(
                            kvp => kvp.Key,
                            kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                        );

                    context.Result = new BadRequestObjectResult(new
                    {
                        Message = "Validation failed",
                        Errors = errors
                    });
                }
                else
                {
                    // For MVC controllers, continue with normal flow
                    // The view will handle displaying validation errors
                }
            }

            base.OnActionExecuting(context);
        }
    }
}
