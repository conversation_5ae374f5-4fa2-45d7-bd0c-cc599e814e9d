using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace SRJ.CommonCore.Validation
{
    /// <summary>
    /// Validation attribute that prevents SQL injection
    /// </summary>
    public class NoSqlInjectionAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value == null || value is not string stringValue)
                return true;

            // Check for SQL injection patterns
            var sqlPatterns = new[]
            {
                @"(\b(ALTER|CREATE|DELETE|DROP|EXEC(UTE)?|INSERT( +INTO)?|MERGE|SELECT|UPDATE|UNION( +ALL)?)\b)",
                @"(\b(AND|OR)\b.*(=|>|<|!=|<>|<=|>=).*(\b(SELECT|UPDATE|DELETE|INSERT|DROP|CREATE|ALTER)\b))",
                @"(\b(CAST|CONVERT|CHAR|NCHAR|VARCHAR|NVARCHAR)\s*\()",
                @"(\b(WAITFOR|DELAY)\s+)",
                @"(\b(OPENROWSET|OPENDATASOURCE)\s*\()",
                @"(\b(sp_|xp_)\w+)",
                @"(;|\||&|--|/\*|\*/)",
                @"(\b(INFORMATION_SCHEMA|SYSOBJECTS|SYSCOLUMNS)\b)",
                @"(\b(HAVING|GROUP\s+BY|ORDER\s+BY)\b)",
                @"('(\s*|\s*.*\s*)(OR|AND)(\s*|\s*.*\s*)')",
                @"(=\s*'.*'(\s*|\s+)(OR|AND)(\s*|\s+)'.*')",
                @"(\b(BENCHMARK|SLEEP|USER|VERSION|DATABASE|SCHEMA)\s*\()",
                @"(1\s*=\s*1|1\s*=\s*'1'|'1'\s*=\s*'1')",
                @"(0\s*=\s*0|0\s*=\s*'0'|'0'\s*=\s*'0')"
            };

            foreach (var pattern in sqlPatterns)
            {
                if (Regex.IsMatch(stringValue, pattern, RegexOptions.IgnoreCase))
                {
                    ErrorMessage = "Input contains potentially dangerous SQL content.";
                    return false;
                }
            }

            return true;
        }
    }
}
