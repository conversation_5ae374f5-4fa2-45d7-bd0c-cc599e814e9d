﻿@using SRJ.DataAccess.Enums
@using SRJ.DataAccess.Entities;
@using SRJ.DataAccess.Entities.Application;
@using SRJ.CommonCore.ViewModels.Application;
@using System.ComponentModel.DataAnnotations
@model IEnumerable<ApplicationFormVM>

@{
    ViewData["Title"] = "Application";
    ViewData["Create"] = true;

    int index = 1;
}

<div class="page-heading">
    <div class="page-title">
        <div class="row">
            <div class="col-12 col-md-6 order-md-1 order-last">
                <h3>Applications List</h3>
                @* <p class="text-subtitle text-muted">For user to check the list</p> *@
            </div>
            <div class="col-12 col-md-6 order-md-2 order-first">
                <nav aria-label="breadcrumb" class="breadcrumb-header float-start float-lg-end">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="Home">Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Applications List</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>
<div class="page-content">
    <section class="section">
        <div id="ApplicationPage">
            <div class="ApplicationPage-form-outer">
                <div class="card">
                    <div class="card-header">
                        <a asp-action="Create" class="btn btn-primary float-sm-right">
                            <i class="bi bi-plus-circle me-1"></i>
                            Create new
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <select id="stageFilter" class="form-control">
                                    <option value="">Filter by Application Stage</option>
                                    @foreach (var stage in Enum.GetValues(typeof(SRJ.DataAccess.Enums.Application.ApplicationStage)))
                                    {
                                        var displayName = stage.GetType().GetField(stage.ToString())
                                        .GetCustomAttributes(typeof(DisplayAttribute), false)
                                        .Cast<DisplayAttribute>().SingleOrDefault()?.Name;

                                        <option value="@stage">@displayName</option>
                                    }
                                </select>
                            </div>

                            <div class="col-md-3">
                                <select id="statusFilter" class="form-control">
                                    <option value="">Filter by Application Status</option>
                                    @foreach (var status in Enum.GetValues(typeof(SRJ.DataAccess.Enums.Application.ApplicationStatus)))
                                    {
                                        var displayName = status.GetType().GetField(status.ToString())
                                        .GetCustomAttributes(typeof(DisplayAttribute), false)
                                        .Cast<DisplayAttribute>().SingleOrDefault()?.Name;

                                        <option value="@status">@displayName</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <table id="datatable" class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Details</th>
                                    <th>Full Name</th>
                                    <th>Civil ID</th>
                                    <th>Gender</th>
                                    <th>Number</th>
                                    <th>Application Stage</th>
                                    <th>Application Status</th>
                                    <th>Agent</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (ApplicationFormVM item in Model)
                                {
                                    <tr>
                                        <td>@(index++)</td>
                                        <td>
                                            <div class="btn-group" role="group" aria-label="Basic example">

                                                <a asp-action="Details" asp-route-id="@item.Id" title="View Details">
                                                    <i class="bi bi-list"></i>
                                                </a>

                                                <a asp-action="Edit" asp-route-id="@item.Id" title="Edit" class="mx-2">
                                                    <i class="bi bi-pencil-square"></i>
                                                </a>

                                                <a asp-action="Delete" asp-route-id="@item.Id" class="delete-button"
                                                   onclick="return confirm('Are you sure you want to delete this Application?');"
                                                   title="Delete">
                                                    <i class="bi bi-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                        <td>@item.Name</td>
                                        <td>@item.CivilId</td>
                                        <td>@item.Gender</td>
                                        <td>@item.MobileNumber</td>
                                        <td>@item.ApplicationStage</td>
                                        <td>@item.ApplicationStatus</td>
                                        <td>@item.Agent</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Initialize the DataTable (using only locally available features)
            var table = $("#datatable").DataTable({
                "responsive": true,
                "lengthChange": true,
                "autoWidth": false,
                "pageLength": 10,
                "order": [[0, "asc"]],
                "columnDefs": [
                    { "orderable": false, "targets": -1 } // Disable ordering on last column (actions)
                ]
            });

            // Custom filtering function for Application Stage and Application Status
            $.fn.dataTable.ext.search.push(
                function (settings, data, dataIndex) {
                    var stageFilter = $('#stageFilter').val();
                    var statusFilter = $('#statusFilter').val();

                    var stage = data[6]; // Application Stage column index
                    var status = data[7]; // Application Status column index

                    if (stageFilter && stageFilter !== stage) {
                        return false;
                    }
                    if (statusFilter && statusFilter !== status) {
                        return false;
                    }
                    return true;
                }
            );

            // Event listeners for filter inputs
            $('#stageFilter, #statusFilter').on('change', function () {
                table.draw();
            });
        });

        // Role-based UI adjustments
        $(document).ready(function () {
            // Fetch the current user's roles
            $.ajax({
                url: '/api/UserRole/roles',
                method: 'GET',
                success: function (roles) {
                    applyRoles(roles);
                },
                error: function () {
                    console.error('Failed to fetch user roles');
                }
            });

            function applyRoles(roles) {
                const adminRole = 'Admin';

                // If the user does not have the admin role, hide the delete buttons
                if (!roles.includes(adminRole)) {
                    $('.delete-button').hide();
                }
            }
        });
    </script>
}