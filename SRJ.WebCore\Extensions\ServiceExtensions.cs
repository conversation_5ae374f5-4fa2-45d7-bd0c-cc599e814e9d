﻿using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Identity.Web;
using SRJ.WebCore.ACL;
using SRJ.DataAccess.Enums;
using SRJ.DataAccess.Identity;
using SRJ.DataAccess.Interfaces.MyFatoorah;
using SRJ.CommonCore.Services;
using SRJ.CommonCore.Services.MyFatoorah;
using SRJ.CommonCore.SettingsModels;
using SRJ.CommonCore.Mappings;
using SRJ.DataAccess;
using SRJ.CommonCore.Interfaces.Account;
using SRJ.CommonCore.Interfaces.Application;
using SRJ.CommonCore.Services.Application;
using SRJ.CommonCore;
using System.Text.Json.Serialization;
using System.Text.Json;
using SRJ.CommonCore.Interfaces;
using SRJ.CommonCore.Services.Account;

namespace SRJ.WebCore.Extensions
{
    public static class ServiceExtensions
    {
        public static void ConfigureRepository(this IServiceCollection services)
        {
            services.AddScoped<IUserProfileService, UserProfileService>(); // Registration of User Profile

            services.AddScoped<IDealsService, DealsService>();

            services.AddTransient<IAccount, AccountService>();

            services.AddScoped<IMvcControllerDiscovery, MvcControllerDiscovery>(); //Access Control

            services.AddScoped(typeof(IFatoorahService<,>), typeof(FatoorahService<,>));

            services.AddScoped<IFileService, FileService>();

            services.AddScoped<IGradeReportService, GradeReportService>(); // Registration of Create Invoice Service

            services.AddScoped<ICustomerReferenceService, CustomerReferenceService>();

            services.AddScoped<ICreateInvoiceService, CreateInvoiceService>();

            services.AddScoped<IStatisticsService, StatisticsService>();

            services.AddScoped(typeof(IGenericService<,>), typeof(GenericService<,>));
        }

        public static void ConfigureSettings(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<MailSettings>(configuration.GetSection("EmailSettings"));

            services.Configure<MyFatoorahSettings>(configuration.GetSection("MyFatoorahSettings"));

            services.Configure<DealApiSettings>(configuration.GetSection("DealApiSettings"));

            services.Configure<MoodleApiSettings>(configuration.GetSection("MoodleApi")); // Configuration for Moodle Api

            services.Configure<ShareFolderPUCSettings>(configuration.GetSection("ShareFolderPUCSettings"));

            services.AddHttpClient<IDealsService, DealsService>();
        }

        public static IServiceCollection ConfigureMSSqlServer(this IServiceCollection services, IConfiguration configuration)
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection");

            services.AddDbContext<ApplicationDbContext>(options =>
              options.UseSqlServer(connectionString));

            return services;
        }

        public static IServiceCollection ConfigureMySqlContext(this IServiceCollection services, IConfiguration config)
        {
            var connectionString = config.GetConnectionString("MySqlDefaultConnection");

            services.AddDbContext<ApplicationDbContext>(opt =>
            {
                opt.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));
            });

            return services;
        }

        public static IServiceCollection ConfigureSQLite(this IServiceCollection services, IConfiguration configuration)
        {
            var connectionString = configuration.GetConnectionString("SQLiteConnection");

            services.AddDbContext<ApplicationDbContext>(options =>
              options.UseSqlite(connectionString));

            return services;
        }

        public static IServiceCollection ConfigureAzureAdAuthentication(this IServiceCollection services, IConfiguration configuration)
        {
            var initialScopes = configuration["DownstreamApi:Scopes"]?.Split(' ') ?? configuration["MicrosoftGraph:Scopes"]?.Split(' ');

            services.AddAuthentication(OpenIdConnectDefaults.AuthenticationScheme)
                .AddMicrosoftIdentityWebApp(options =>
                {
                    configuration.Bind("AzureAd", options);

                    options.Events.OnTokenValidated = async context =>
                    {
                        var userManager = context.HttpContext.RequestServices.GetRequiredService<UserManager<AppUser>>();
                        var roleManager = context.HttpContext.RequestServices.GetRequiredService<RoleManager<AppRole>>();
                        var emailClaim = context.Principal.FindFirst("preferred_username")?.Value;
                        var claims = context.Principal.Claims.ToList();

                        // The OID claim is a stable identifier for the user.
                        var oidClaim = claims.FirstOrDefault(c => c.Type == "http://schemas.microsoft.com/identity/claims/objectidentifier")?.Value;

                        if (string.IsNullOrEmpty(oidClaim))
                        {
                            context.Fail("OID claim not found.");
                            return;
                        }

                        AppUser user = await userManager.Users.FirstOrDefaultAsync(u => u.AzureAdObjectId == Guid.Parse(oidClaim));
                        if (user == null)
                        {
                            var nameClaim = claims.FirstOrDefault(c => c.Type == "name")?.Value;

                            // Create a new local user account for the Azure AD user
                            user = new AppUser
                            {
                                FirstName = nameClaim?.Split(' ')[0],
                                LastName = nameClaim?.Split(' ').Skip(1).Any() == true ? string.Join(" ", nameClaim.Split(' ').Skip(1)) : "",
                                Status = UserStatus.Active,
                                CreatedAt = DateTime.UtcNow.ToLocalTime(),
                                UserName = emailClaim,
                                Email = emailClaim,
                                AzureAdObjectId = Guid.Parse(oidClaim)
                            };
                            var createUserResult = await userManager.CreateAsync(user);
                            if (!createUserResult.Succeeded)
                            {
                                context.Fail("Failed to create local user account.");
                                return;
                            }
                        }

                        // Default role logic
                        string roleToAssign = "Default"; // Default to Everyone

                        // Get admin emails from configuration
                        var adminEmails = configuration.GetSection("AdminEmails").Get<string[]>() ?? Array.Empty<string>();

                        if (!string.IsNullOrEmpty(emailClaim))
                        {
                            if (adminEmails.Contains(emailClaim, StringComparer.OrdinalIgnoreCase))
                            {
                                roleToAssign = "Admin";
                            }
                            else if (char.IsDigit(emailClaim.FirstOrDefault()))
                            {
                                roleToAssign = "Student";
                            }
                        }

                        // Ensure the role exists
                        if (!await roleManager.RoleExistsAsync(roleToAssign))
                        {
                            await roleManager.CreateAsync(new AppRole { Name = roleToAssign });
                        }

                        // Assign the role if not already assigned
                        if (!await userManager.IsInRoleAsync(user, roleToAssign))
                        {
                            await userManager.AddToRoleAsync(user, roleToAssign);
                        }
                    };
                })
                .EnableTokenAcquisitionToCallDownstreamApi(initialScopes)
                .AddMicrosoftGraph(configuration.GetSection("MicrosoftGraph"))
                .AddInMemoryTokenCaches();

            // Set global authorization policy to use OpenIdConnectDefaults.AuthenticationScheme
            services.AddControllersWithViews(options =>
            {
                var policy = new AuthorizationPolicyBuilder()
                    .RequireAuthenticatedUser()
                    .AddAuthenticationSchemes(OpenIdConnectDefaults.AuthenticationScheme)
                    .Build();

                options.Filters.Add(new AuthorizeFilter(policy));
            });

            return services;
        }

        public static IServiceCollection ConfigureIdentity(this IServiceCollection services)
        {
            services.AddIdentity<AppUser, AppRole>(options =>
            {
                // Enhanced password policy
                options.Password.RequiredLength = 8;
                options.Password.RequireDigit = true;
                options.Password.RequireLowercase = true;
                options.Password.RequireUppercase = true;
                options.Password.RequireNonAlphanumeric = true;
                options.Password.RequiredUniqueChars = 6;

                // Account lockout settings
                options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(15);
                options.Lockout.MaxFailedAccessAttempts = 5;
                options.Lockout.AllowedForNewUsers = true;

                // User settings
                options.User.RequireUniqueEmail = true;

                options.SignIn.RequireConfirmedAccount = false;
                options.SignIn.RequireConfirmedPhoneNumber = false;
                options.SignIn.RequireConfirmedEmail = false;

            }).AddEntityFrameworkStores<ApplicationDbContext>()
            .AddTokenProvider<DataProtectorTokenProvider<AppUser>>(TokenOptions.DefaultProvider)
            .AddDefaultTokenProviders();

            return services;
        }

        public static IServiceCollection ConfigureCookies(this IServiceCollection services)
        {
            // Cookies and Account Routes
            services.ConfigureApplicationCookie(options =>
            {
                options.AccessDeniedPath = "/Error/403";
            });

            return services;
        }

        public static IServiceCollection ConfigureAutoMapper(this IServiceCollection services)
        {
            services.AddAutoMapper(typeof(MappingProfile));
            return services;
        }

        public static IServiceCollection AddControllersWithCustomJsonOptions(this IServiceCollection services)
        {
            services.AddControllers().AddJsonOptions(opt =>
            {
                var enumConverter = new JsonStringEnumConverter(JsonNamingPolicy.CamelCase);
                opt.JsonSerializerOptions.Converters.Add(enumConverter);
            });

            return services;
        }
    }
}
